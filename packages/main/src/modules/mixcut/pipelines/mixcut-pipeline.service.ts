import { Injectable } from '@nestjs/common'
import { MixcutableOverlay } from '@app/shared/types/ipc/mixcut.js'
import { MIXCUT_PIPELINES, MixcutPipelines } from '@app/shared/types/mixcut.js'
import { SimpleOverlaysFilterProcessingPipeline } from '@/modules/mixcut/pipelines/pipelines.js'
import { OverlayType } from '@app/shared/types/overlay.js'
import {
  VideoPositionOffsetProcessor,
  VideoRotationProcessor,
  VideoScaleProcessor,
  VideoSpeedProcessor
} from '../overlay-processors/index.js'
import { MixcutPipelineContext, MixcutPipelineProcessor } from '@/infra/types/mixcut/mixcut-pipeline.js'

const videoOverlayFilter = (o: MixcutableOverlay) => o.type === OverlayType.VIDEO

const PIPELINE_HANDLER_BY_NAME: Partial<Record<MixcutPipelines, (context: MixcutPipelineContext) => MixcutPipelineProcessor>> = {
  [MIXCUT_PIPELINES.video.rotation]: context => new SimpleOverlaysFilterProcessingPipeline(context).apply(VideoRotationProcessor, videoOverlayFilter),
  [MIXCUT_PIPELINES.video.scale]: context => new SimpleOverlaysFilterProcessingPipeline(context).apply(VideoScaleProcessor, videoOverlayFilter),
  [MIXCUT_PIPELINES.video.positionOffset]: context => new SimpleOverlaysFilterProcessingPipeline(context).apply(VideoPositionOffsetProcessor, videoOverlayFilter),
  [MIXCUT_PIPELINES.video.speed]: context => new SimpleOverlaysFilterProcessingPipeline(context).apply(VideoSpeedProcessor, videoOverlayFilter),
}

@Injectable()
export class MixcutPipelineService {

  public async process(
    context: MixcutPipelineContext,
    overlays: MixcutableOverlay[],
    pipelines: Array<{ name: MixcutPipelines, config?: any }>,
  ) {
    for (const pipeline of pipelines) {
      const processor = PIPELINE_HANDLER_BY_NAME[pipeline.name]?.(context)
      if (!processor) {
        continue
      }

      overlays = await processor.process(overlays)
    }

    return overlays
  }
}
