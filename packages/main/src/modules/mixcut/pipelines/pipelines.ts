import { Overlay } from '@app/shared/types/overlay.js'
import { MixcutPipelineProcessor, SingleOverlayProcessor } from '@/infra/types/mixcut/mixcut-pipeline.js'

/**
 * 视频随机开头
 * @description 从所有视频中随机挑选一个，截取一定的帧数，并作随机缩放/旋转后，插入到整个视频的开头（替换第一个分镜的起始部分帧）
 */
class RandomBeginningPipeline extends MixcutPipelineProcessor {

  process(overlays: Overlay[]): Promise<Overlay[]> {
    // TODO
    throw new Error('Method not implemented.')
  }
}

/**
 * 全局背景
 * @description 为整个视频选择一种背景渲染方式（纯色、模糊、图片等）
 */
class GlobalBackgroundPipeline extends MixcutPipelineProcessor {

  process(overlays: Overlay[]): Promise<Overlay[]> {
    // TODO
    throw new Error('Method not implemented.')
  }
}

/**
 * ? 无序混剪素材
 * @description ? 开启后，将指定分镜打乱顺序进行排列组合
 */
// class ShuffleVideosPipeline extends MixcutProcessPipeline {
//
//   process(overlays: Overlay[]): Overlay[] | Promise<Overlay[]> {
//     // TODO
//     throw new Error('Method not implemented.')
//   }
// }

/**
 * 全局背景音乐
 *
 */
class GlobalBackgroundMusicPipeline extends MixcutPipelineProcessor {

  process(overlays: Overlay[]): Promise<Overlay[]> {
    // TODO
    throw new Error('Method not implemented.')
  }
}

export class SimpleOverlaysFilterProcessingPipeline extends MixcutPipelineProcessor {

  private processor?: SingleOverlayProcessor = undefined
  private filter?: (o: Overlay) => boolean = undefined

  public apply(
    processorBuilder: new (pipeline: MixcutPipelineProcessor) => SingleOverlayProcessor,
    filter: (o: Overlay) => boolean
  ) {
    this.processor = new processorBuilder(this)
    this.filter = filter
    
    return this
  }

  public process(overlays: Overlay[]): Promise<Overlay[]> {
    if (!this.processor || !this.filter) {
      throw new Error('Processor not set')
    }

    return Promise.all(
      overlays
        .filter(this.filter)
        .map(overlay => this.processor!.process(overlay))
    )
  }
}
