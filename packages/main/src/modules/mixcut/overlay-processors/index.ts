import { SingleOverlayProcessor } from '@/infra/types/mixcut/mixcut-pipeline.js'
import { SoundOverlay, TextOverlay, VideoOverlay } from '@app/shared/types/overlay.js'
import _ from 'lodash'

/**
 * 视频旋转
 * @description 每个视频随机旋转角度后并自适应填充满画布
 */
export class VideoRotationProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly RANGE = 30

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const { playerMetadata: { width: playerWidth, height: playerHeight } } = this.pipeline.context

    // 检测当前 VideoOverlay 是否已经撑满播放器画布
    if (!this.canApplyProcessor(overlay, playerWidth, playerHeight)) {
      // 如果未撑满播放器，跳过旋转处理，直接返回原始 overlay
      return overlay
    }

    // 生成随机旋转角度
    const randomRotation = _.random(-this.RANGE, this.RANGE)
    const newRotation = overlay.rotation + randomRotation

    // 计算旋转后为了填满画布所需的最小缩放比例
    const scaleCompensation = this.calculateRotationScaleCompensation(
      overlay.width,
      overlay.height,
      newRotation,
      playerWidth,
      playerHeight
    )

    console.log({ scaleCompensation })

    // 应用缩放补偿到 overlay 的尺寸
    const newWidth = Math.round(overlay.width * scaleCompensation)
    const newHeight = Math.round(overlay.height * scaleCompensation)

    // 重新计算位置以保持居中
    const newLeft = overlay.left - (newWidth - overlay.width) / 2
    const newTop = overlay.top - (newHeight - overlay.height) / 2

    return {
      ...overlay,
      rotation: newRotation,
      width: newWidth,
      height: newHeight,
      left: Math.round(newLeft),
      top: Math.round(newTop),
    }
  }

  private canApplyProcessor(overlay: VideoOverlay, playerWidth: number, playerHeight: number): boolean {
    // 当
    if (overlay.rotation !== 0) return false

    // 检查 overlay 是否完全覆盖播放器画布
    // 考虑一定的容差值（1像素）来处理浮点数精度问题
    const tolerance = 1

    const overlayLeft = overlay.left
    const overlayTop = overlay.top
    const overlayRight = overlay.left + overlay.width
    const overlayBottom = overlay.top + overlay.height

    // overlay 需要完全覆盖播放器画布才算撑满
    return (
      overlayLeft <= tolerance &&
      overlayTop <= tolerance &&
      overlayRight >= playerWidth - tolerance &&
      overlayBottom >= playerHeight - tolerance
    )
  }

  /**
   * 计算旋转后为了填满画布所需的缩放比例
   * @param width overlay 宽度
   * @param height overlay 高度
   * @param rotationDegrees 旋转角度（度）
   * @param canvasWidth 画布宽度
   * @param canvasHeight 画布高度
   * @returns 所需的缩放比例
   */
  private calculateRotationScaleCompensation(
    width: number,
    height: number,
    rotationDegrees: number,
    canvasWidth: number,
    canvasHeight: number
  ): number {
    // 将角度转换为弧度
    const rotationRadians = (rotationDegrees * Math.PI) / 180

    // 计算旋转后的边界框尺寸
    const cos = Math.abs(Math.cos(rotationRadians))
    const sin = Math.abs(Math.sin(rotationRadians))

    const rotatedWidth = width * cos + height * sin
    const rotatedHeight = width * sin + height * cos

    // 计算需要的缩放比例以填满画布
    const scaleX = canvasWidth / rotatedWidth
    const scaleY = canvasHeight / rotatedHeight

    // 使用较大的缩放比例确保完全填满画布
    const scale = Math.max(scaleX, scaleY)

    // 使用 clamp 限制缩放比例在合理范围内（最小1.0，最大3.0）
    return _.clamp(scale, 1.0, 3.0)
  }
}

/**
 * 视频缩放
 * @description 每个视频随机放大素材画布比例
 */
export class VideoScaleProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly SCALE_RANGE = 0.5

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const scale = 1 + _.random(-this.SCALE_RANGE, this.SCALE_RANGE)

    return {
      ...overlay,
      width: Math.round(overlay.width * scale),
      height: Math.round(overlay.height * scale)
    }
  }
}

/**
 * 视频位置偏移
 * @description 每个视频随机位移画布位置并自适应填充画布
 */
export class VideoPositionOffsetProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly POSITION_OFFSET_RANGE = 0.2

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const offsetPercent = _.random(-this.POSITION_OFFSET_RANGE, this.POSITION_OFFSET_RANGE)

    return {
      ...overlay,
      left: Math.round(overlay.left * (1 + offsetPercent)),
      top: Math.round(overlay.top * (1 + offsetPercent)),
      // TODO: 放大以填充画布
    }
  }
}

/**
 * 视频镜像
 * @description 将视频画面水平镜像
 */
export class VideoFlipProcessor extends SingleOverlayProcessor<VideoOverlay> {

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const shouldFlip = Math.random() > 0.5

    return {
      ...overlay,
      styles: {
        ...overlay.styles,
        // TODO: 判断原始的状态
        transform: shouldFlip ? 'scaleX(-1)' : ''
      }
    }
  }
}

/**
 * 分镜素材智能截取
 * @description 开启后，系统将分镜里的素材，如分镜时长5s，放入10秒的素材，将从10s的素材中智能随机选取5秒;若不开启则默认从头截取5秒
 */
export class VideoSmartClipProcessor extends SingleOverlayProcessor<VideoOverlay> {

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    // TODO
    return overlay
  }
}

/**
 * 视频变速
 * @description 每个混剪视频整体时长随机进行1.1x-1.2x变速处理
 */
export class VideoSpeedProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly SPEED_CHANGE_RANGE = 1

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const speedChangeMultiplier = 1 + _.random(-this.SPEED_CHANGE_RANGE, this.SPEED_CHANGE_RANGE)

    const newSpeed = (overlay.speed ?? 1) * speedChangeMultiplier

    return {
      ...overlay,
      speed: newSpeed,
      durationInFrames: overlay.durationInFrames / speedChangeMultiplier
    }
  }
}

/**
 * 去片尾
 * @description 去除视频最后的0.1-0.2s随机取值
 */
export class VideoTrimEndProcessor extends SingleOverlayProcessor<VideoOverlay> {

  // TODO: 修改到合适的值
  private readonly RANGE_MIN = 30
  private readonly RANGE_MAX = 150

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    const { trimEndFrames = 0, speed = 1, durationInFrames } = overlay
    const additionalTrimEnd = _.random(this.RANGE_MIN, this.RANGE_MAX)
    const trimEnd = (trimEndFrames ?? 0) + additionalTrimEnd

    return {
      ...overlay,
      trimEndFrames: trimEnd,
      // TODO: 更改变速值以保证 `durationInFrames` 不变
      // speed:
      // durationInFrames: durationInFrames - additionalTrimEnd / speed
    }
  }
}

/**
 * 透明蒙版
 * @description 给每个视频随机添加不同的透明蒙版 (在视频上面叠半透明视频)
 */
export class VideoMaskingProcessor extends SingleOverlayProcessor<VideoOverlay> {

  async process(overlay: VideoOverlay): Promise<VideoOverlay> {
    // TODO
    return overlay
  }
}

/**
 * 口播字幕字体
 * @description 为所有口播字幕统一更换字体
 */
export class NarrationFontFamilyProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/**
 * 口播字幕花体字样式
 * @description 为所有口播字幕统一更换花体字样式
 */
export class NarrationStyledTextProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/**
 * 全局文字字体
 */
export class GlobalTextFontFamilyProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/**
 * 全局文字花体字样式
 */
export class GlobalTextStyledTextProcessor extends SingleOverlayProcessor<TextOverlay> {

  async process(overlay: TextOverlay): Promise<TextOverlay> {
    // TODO
    return overlay
  }
}

/**
 * 口播音色
 * @description 为口播轨道中的音频重新选择音色并生成配音
 */
export class NarrationTimbreProcessor extends SingleOverlayProcessor<SoundOverlay> {

  async process(overlay: SoundOverlay): Promise<SoundOverlay> {
    // TODO
    return overlay
  }
}
