import { Overlay } from '@app/shared/types/overlay.js'
import { PlayerMetadata } from '@app/shared/types/ipc/mixcut.js'

export type MixcutPipelineContext = {
  playerMetadata: PlayerMetadata
}

export abstract class MixcutPipelineProcessor {

  constructor(public readonly context: MixcutPipelineContext) {
  }

  abstract process(overlays: Overlay[]): Promise<Overlay[]>
}

export abstract class SingleOverlayProcessor<TLimitedOverlay extends Overlay = Overlay> {

  constructor(public pipeline: MixcutPipelineProcessor) {
  }

  abstract process(overlays: TLimitedOverlay): Promise<TLimitedOverlay>
}
