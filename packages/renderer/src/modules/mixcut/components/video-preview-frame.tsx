import React, { useMemo } from 'react'
import { VideoOverlay } from '@app/shared/types/overlay'
import { useLoadTileImageInfo } from '@/hooks/material/useLoadTileImageInfo'
import { TileRenderer } from '@/components/material/TileRenderer'

const fallback = (
  <div className="w-full h-full bg-gray-500 flex items-center justify-center">
    <div className="text-white/60 text-sm">无预览</div>
  </div>
)

export const VideoPreviewFrame: React.FC<{ overlay?: VideoOverlay }> = ({ overlay }) => {
  if (!overlay?.originalMeta.tileUrl) return fallback

  const { width, height } = overlay.originalMeta

  const tileInfo = useLoadTileImageInfo(overlay.originalMeta.tileUrl)

  const currentFrame = useMemo(() => {
    if (!tileInfo) return 0

    const { trimStartFrames = 0, originalDurationFrames } = overlay

    return Math.floor(trimStartFrames / originalDurationFrames * tileInfo.totalFrames)
  }, [overlay, tileInfo])

  if (!tileInfo) {
    return fallback
  }

  return (
    <div className="w-full h-full bg-gray-500 flex items-center justify-center relative overflow-hidden rounded-sm">
      <TileRenderer tileInfo={tileInfo} currentFrame={currentFrame} aspectRatio={width / height} />
    </div>
  )
}
